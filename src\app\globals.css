@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

html {
  scroll-behavior: smooth;
  height: 100%;
}

body {
   background-color: #fff;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  color: #171717;
  font-family: 'Roboto',sans-serif;
  font-weight: 500;
  font-style: normal;
  font-size: 12px;
  line-height: 140%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  min-height: 100%;
  position: relative;
  overflow-x: hidden;
}

/* Ensure sticky header works properly */
header.sticky {
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 50;
}

/* Add smooth scrolling */
.scroll-smooth {
  scroll-behavior: smooth;
}

