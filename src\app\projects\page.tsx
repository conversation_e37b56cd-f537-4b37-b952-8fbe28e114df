import React from 'react';
import SectionHeading from '@/components/ui/SectionHeading';
import ProjectCard from '@/components/ui/ProjectCard';
import CTABlock from '@/components/ui/CTABlock';

export default function ProjectsPage() {
  // Sample data - would be fetched from Sanity CMS in production
  const projects = [
    {
      title: "University Athletic Complex",
      category: "Basketball Courts",
      imageUrl: "/images/placeholder-project-1.jpg",
      projectUrl: "/projects/university-athletic-complex"
    },
    {
      title: "Community Aquatic Center",
      category: "Swimming Pools",
      imageUrl: "/images/placeholder-project-2.jpg",
      projectUrl: "/projects/community-aquatic-center"
    },
    {
      title: "Municipal Sports Park",
      category: "Soccer Fields",
      imageUrl: "/images/placeholder-project-3.jpg",
      projectUrl: "/projects/municipal-sports-park"
    },
    {
      title: "Elementary School Playground",
      category: "Playgrounds",
      imageUrl: "/images/placeholder-project-4.jpg",
      projectUrl: "/projects/elementary-school-playground"
    },
    {
      title: "Regional Sports Arena",
      category: "Arena Design-Build",
      imageUrl: "/images/placeholder-project-5.jpg",
      projectUrl: "/projects/regional-sports-arena"
    },
    {
      title: "College Recreation Center",
      category: "Equipment & Gear",
      imageUrl: "/images/placeholder-project-6.jpg",
      projectUrl: "/projects/college-recreation-center"
    }
  ];

  // Categories for filter
  const categories = [
    "All Projects",
    "Basketball Courts",
    "Swimming Pools",
    "Playgrounds",
    "Soccer Fields",
    "Equipment & Gear",
    "Arena Design-Build"
  ];

  return (
    <div className="bg-brand-white">
      {/* Page Header */}
      <div className="bg-brand-dark text-brand-white py-16 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto max-w-4xl text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">Our Portfolio</h1>
          <p className="text-xl text-brand-lighterGray">
            Explore our completed projects and see how we've helped clients achieve their vision.
          </p>
        </div>
      </div>

      {/* Filter Section */}
      <section className="py-8 px-4 sm:px-6 lg:px-8 bg-brand-offWhite">
        <div className="container mx-auto">
          <div className="flex flex-wrap justify-center gap-4">
            {categories.map((category, index) => (
              <button
                key={index}
                className={`px-4 py-2 rounded-md text-sm font-medium ${
                  index === 0 
                    ? 'bg-brand-accent text-brand-white' 
                    : 'bg-brand-white text-brand-secondary hover:bg-brand-accent hover:text-brand-white'
                } transition-colors`}
              >
                {category}
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* Projects Grid */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {projects.map((project, index) => (
              <ProjectCard
                key={index}
                title={project.title}
                category={project.category}
                imageUrl={project.imageUrl}
                projectUrl={project.projectUrl}
              />
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <CTABlock
        headline="Ready to Start Your Project?"
        description="Contact our team today to discuss how we can bring your sports facility vision to life."
        buttonText="Get in Touch"
        buttonLink="/contact"
        appearance="primary"
      />
    </div>
  );
}
