import React from 'react';
import LeadershipTeamSection from '@/components/ui/LeadershipTeamSection';
import SectionHeading from '@/components/ui/SectionHeading';
import CTABlock from '@/components/ui/CTABlock';

export default function AboutPage() {
  // Sample data - would be fetched from Sanity CMS in production
  const teamMembers = [
    {
      name: "<PERSON>",
      title: "Senior Project Manager, Construction",
      imageUrl: "/images/team/frank-domingo.jpg",
      accentColor: "#e74c3c" // Orange accent
    },
    {
      name: "<PERSON>",
      title: "Sector Lead, Sports Facilities",
      imageUrl: "/images/team/kate-jack.jpg",
      accentColor: "#e74c3c" // Orange accent
    },
    {
      name: "<PERSON>",
      title: "Design Director",
      imageUrl: "/images/team/ashley-thompson.jpg",
      accentColor: "#e74c3c" // Orange accent
    },
    {
      name: "<PERSON>",
      title: "Senior Principal, Innovation Leader",
      imageUrl: "/images/team/pamela-bailey.jpg",
      accentColor: "#e74c3c" // Orange accent
    },
    {
      name: "<PERSON>",
      title: "Principal, Engineering Lead",
      imageUrl: "/images/team/mike-voll.jpg",
      accentColor: "#e74c3c" // Orange accent
    }
  ];

  return (
    <main className="bg-brand-white">
      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-brand-offWhite">
        <div className="container mx-auto">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">About Us</h1>
            <p className="text-xl text-brand-secondary">
              We're a team of dedicated professionals committed to building exceptional sports facilities that inspire performance and community.
            </p>
          </div>
        </div>
      </section>
      
      {/* Company Story Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <SectionHeading
                title="Our Story"
                subtitle="Building champions since 1995"
                alignment="left"
              />
              
              <div className="mt-6 space-y-6 text-brand-secondary">
                <p>
                  Founded over 25 years ago with a vision to transform the landscape of sports facility construction, our company has grown from a small team focused on local basketball courts to an industry leader delivering comprehensive sports infrastructure solutions nationwide.
                </p>
                <p>
                  Our journey began when our founder, a former collegiate athlete, recognized the profound impact that well-designed sports facilities have on athletic performance, community engagement, and public health. This insight drove us to assemble a team of specialists who share a passion for sports and excellence in construction.
                </p>
                <p>
                  Today, we pride ourselves on our end-to-end approach, handling everything from initial concept and design to construction and ongoing maintenance. Our portfolio spans hundreds of successful projects, from Olympic-caliber swimming pools to community playgrounds, professional soccer fields to multi-purpose arenas.
                </p>
                <p>
                  What sets us apart is our unwavering commitment to quality, innovation, and client satisfaction. We don't just build facilities; we create spaces where athletes can excel, communities can gather, and memories can be made.
                </p>
              </div>
            </div>
            
            <div className="relative h-96 bg-gray-200 rounded-lg overflow-hidden">
              {/* Placeholder for image - in production would use next/image with proper src */}
              <div className="absolute inset-0 flex items-center justify-center text-gray-400">
                [Company History Image]
              </div>
            </div>
          </div>
        </div>
      </section>
      
      {/* Mission & Values Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-brand-offWhite">
        <div className="container mx-auto">
          <SectionHeading
            title="Our Mission & Values"
            subtitle="The principles that guide everything we do"
            alignment="center"
          />
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-12">
            <div className="bg-white p-8 rounded-lg shadow-sm">
              <h3 className="text-xl font-bold mb-4 text-brand-primary">Excellence</h3>
              <p className="text-brand-secondary">
                We are committed to delivering the highest quality in every aspect of our work, from materials and construction techniques to client communication and project management.
              </p>
            </div>
            
            <div className="bg-white p-8 rounded-lg shadow-sm">
              <h3 className="text-xl font-bold mb-4 text-brand-primary">Innovation</h3>
              <p className="text-brand-secondary">
                We continuously seek new technologies, materials, and methods to enhance the performance, sustainability, and user experience of the facilities we build.
              </p>
            </div>
            
            <div className="bg-white p-8 rounded-lg shadow-sm">
              <h3 className="text-xl font-bold mb-4 text-brand-primary">Integrity</h3>
              <p className="text-brand-secondary">
                We operate with transparency, honesty, and ethical standards in all our dealings, building trust with clients, partners, and communities.
              </p>
            </div>
            
            <div className="bg-white p-8 rounded-lg shadow-sm">
              <h3 className="text-xl font-bold mb-4 text-brand-primary">Collaboration</h3>
              <p className="text-brand-secondary">
                We believe in the power of teamwork, partnering closely with clients, architects, engineers, and stakeholders to achieve shared goals.
              </p>
            </div>
            
            <div className="bg-white p-8 rounded-lg shadow-sm">
              <h3 className="text-xl font-bold mb-4 text-brand-primary">Community Impact</h3>
              <p className="text-brand-secondary">
                We recognize that sports facilities are more than structures—they're catalysts for community development, public health, and social connection.
              </p>
            </div>
            
            <div className="bg-white p-8 rounded-lg shadow-sm">
              <h3 className="text-xl font-bold mb-4 text-brand-primary">Sustainability</h3>
              <p className="text-brand-secondary">
                We design and build with environmental responsibility in mind, minimizing ecological impact while maximizing resource efficiency and longevity.
              </p>
            </div>
          </div>
        </div>
      </section>
      
      {/* Leadership Team Section - NEW COMPONENT */}
      <LeadershipTeamSection 
        headline="Our Leadership Team"
        teamMembers={teamMembers}
      />
      
      {/* CTA Section */}
      <CTABlock
        headline="Ready to Work With Our Team?"
        description="Contact us today to discuss your sports facility project and discover how our expertise can bring your vision to life."
        buttonText="Get in Touch"
        buttonLink="/contact"
        appearance="primary"
      />
    </main>
  );
}
