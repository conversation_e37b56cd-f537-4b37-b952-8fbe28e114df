"use client";

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';

interface SlideData {
  id: number;
  category: string;
  title: string;
  buttonText: string;
  buttonLink: string;
  image: string;
  backgroundColor: string;
}

const HeroSlider: React.FC = () => {
  // Sample slide data - would be fetched from Sanity CMS in production
  const slides: SlideData[] = [
    {
      id: 1,
      category: 'INNOVATION',
      title: 'Sparking Innovation: Sports Facility Excellence',
      buttonText: 'LEARN MORE',
      buttonLink: '/services',
      image: '/slides/slide12.jpg',
      backgroundColor: '#e74c3c' // A vibrant red-orange similar to the reference
    },
    {
      id: 2,
      category: 'BASKETBALL COURTS',
      title: 'Professional-Grade Courts Built to Last',
      buttonText: 'VIEW PROJECTS',
      buttonLink: '/projects',
      image: '/slides/slide9.jpg',
      backgroundColor: '#17A2B8' // Our accent color
    },
    {
      id: 3,
      category: 'SWIMMING POOLS',
      title: 'Olympic Quality Pools for Every Community',
      buttonText: 'EXPLORE SOLUTIONS',
      buttonLink: '/services',
      image: '/slides/slide11.jpg',
      backgroundColor: '#2ecc71' // A complementary green
    },
    {
      id: 4,
      category: 'PLAYGROUNDS',
      title: 'Safe, Engaging Spaces for Young Athletes',
      buttonText: 'SEE GALLERY',
      buttonLink: '/gallery',
      image: '/slides/slide1.jpg',
      backgroundColor: '#9b59b6' // A complementary purple
    },
    {
      id: 5,
      category: 'EXPERTISE',
      title: 'End-to-End Sports Facility Construction',
      buttonText: 'CONTACT US',
      buttonLink: '/contact',
      image: '/slides/slide6.jpg',
      backgroundColor: '#f39c12' // A warm amber
    }
  ];

  const [currentSlide, setCurrentSlide] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Auto-advance slides
  useEffect(() => {
    const interval = setInterval(() => {
      nextSlide();
    }, 5000); // Change slide every 5 seconds

    return () => clearInterval(interval);
  }, [currentSlide]);

  const nextSlide = () => {
    if (!isTransitioning) {
      setIsTransitioning(true);
      setCurrentSlide((prev) => (prev === slides.length - 1 ? 0 : prev + 1));
      setTimeout(() => setIsTransitioning(false), 500); // Match transition duration
    }
  };

  const prevSlide = () => {
    if (!isTransitioning) {
      setIsTransitioning(true);
      setCurrentSlide((prev) => (prev === 0 ? slides.length - 1 : prev - 1));
      setTimeout(() => setIsTransitioning(false), 500); // Match transition duration
    }
  };

  const goToSlide = (index: number) => {
    if (!isTransitioning && index !== currentSlide) {
      setIsTransitioning(true);
      setCurrentSlide(index);
      setTimeout(() => setIsTransitioning(false), 500); // Match transition duration
    }
  };

  // Format the slide number with leading zero
  const formatSlideNumber = (num: number) => {
    return num < 10 ? `0${num}` : num;
  };

  return (
    <div className="relative w-full h-[600px] md:h-[700px] overflow-hidden">
      {/* Slides */}
      {slides.map((slide, index) => (
        <div
          key={slide.id}
          className={`absolute inset-0 w-full h-full flex transition-opacity duration-500 ease-in-out ${
            index === currentSlide ? 'opacity-100 z-10' : 'opacity-0 z-0'
          }`}
        >
          {/* Left Content Panel */}
          <div 
            className="w-full md:w-1/2 flex flex-col justify-center p-8 md:p-16"
            style={{ backgroundColor: slide.backgroundColor }}
          >
            <div className="text-white">
              <p className="text-sm md:text-base font-medium tracking-wider mb-4">
                {slide.category}
              </p>
              <h2 className="text-3xl md:text-5xl font-bold mb-8 leading-tight">
                {slide.title}
              </h2>
              <Link 
                href={slide.buttonLink}
                className="inline-block border-2 border-white text-white px-8 py-3 text-sm font-medium tracking-wider hover:bg-white hover:text-black transition-colors duration-300"
              >
                {slide.buttonText}
              </Link>
            </div>
          </div>
          
          {/* Right Image Panel */}
          <div className="hidden md:block w-1/2 relative">
            <Image
              src={slide.image}
              alt={slide.title}
              fill
              className="object-cover"
              priority={index === 0}
            />
          </div>
        </div>
      ))}
      
      {/* Navigation Controls */}
      <div className="absolute bottom-8 left-8 md:left-16 z-20 flex items-center space-x-6 text-white">
        <div className="flex items-center space-x-2">
          <span className="text-xl font-bold">
            {formatSlideNumber(currentSlide + 1)}
          </span>
          <div className="w-12 h-px bg-white/50"></div>
          <span className="text-xl text-white/50">
            {formatSlideNumber(slides.length)}
          </span>
        </div>
      </div>
      
      {/* Arrow Controls */}
      <div className="absolute bottom-8 right-8 md:right-16 z-20 flex items-center space-x-4">
        <button 
          onClick={prevSlide}
          className="w-12 h-12 rounded-full border-2 border-white flex items-center justify-center text-white hover:bg-white hover:text-black transition-colors duration-300"
          aria-label="Previous slide"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        <button 
          onClick={nextSlide}
          className="w-12 h-12 rounded-full border-2 border-white flex items-center justify-center text-white hover:bg-white hover:text-black transition-colors duration-300"
          aria-label="Next slide"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>
    </div>
  );
};

export default HeroSlider;

