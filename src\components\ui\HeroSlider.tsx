"use client";

import React, { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import Link from 'next/link';

interface SlideData {
  id: number;
  category: string;
  title: string;
  buttonText: string;
  buttonLink: string;
  image: string;
  backgroundColor: string;
}

const HeroSlider: React.FC = () => {
  // Updated slide data to match the mining/engineering theme
  const slides: SlideData[] = [
    {
      id: 1,
      category: 'MINING, MINERALS & METALS',
      title: 'More than mining: Engineering some of the world\'s largest mine sites',
      buttonText: 'LEARN MORE',
      buttonLink: '/services',
      image: '/slides/slide12.jpg',
      backgroundColor: '#000000' // Black background like in the image
    },
    {
      id: 2,
      category: 'INFRASTRUCTURE',
      title: 'Building Tomorrow: Advanced Engineering Solutions',
      buttonText: 'VIEW PROJECTS',
      buttonLink: '/projects',
      image: '/slides/slide9.jpg',
      backgroundColor: '#000000'
    },
    {
      id: 3,
      category: 'TECHNOLOGY',
      title: 'Innovation Driven: Smart Mining Technologies',
      buttonText: 'EXPLORE SOLUTIONS',
      buttonLink: '/services',
      image: '/slides/slide11.jpg',
      backgroundColor: '#000000'
    },
    {
      id: 4,
      category: 'SUSTAINABILITY',
      title: 'Responsible Mining: Environmental Excellence',
      buttonText: 'SEE GALLERY',
      buttonLink: '/gallery',
      image: '/slides/slide1.jpg',
      backgroundColor: '#000000'
    },
    {
      id: 5,
      category: 'EXPERTISE',
      title: 'Global Reach: Engineering Excellence Worldwide',
      buttonText: 'CONTACT US',
      buttonLink: '/contact',
      image: '/slides/slide6.jpg',
      backgroundColor: '#000000'
    }
  ];

  const [currentSlide, setCurrentSlide] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [leftPanelVisible, setLeftPanelVisible] = useState(false);
  const [progress, setProgress] = useState(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const progressRef = useRef<NodeJS.Timeout | null>(null);

  const SLIDE_DURATION = 5000; // 5 seconds per slide
  const PROGRESS_INTERVAL = 50; // Update progress every 50ms

  const startProgressTimer = () => {
    setProgress(0);
    if (progressRef.current) clearInterval(progressRef.current);

    progressRef.current = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          // Auto advance to next slide
          if (!isTransitioning) {
            setIsTransitioning(true);
            setLeftPanelVisible(false);

            setTimeout(() => {
              setCurrentSlide((prevSlide) => (prevSlide === slides.length - 1 ? 0 : prevSlide + 1));
              setImageLoaded(false);

              setTimeout(() => {
                setImageLoaded(true);
                setTimeout(() => {
                  setLeftPanelVisible(true);
                  setIsTransitioning(false);
                  startProgressTimer();
                }, 300);
              }, 200);
            }, 300);
          }
          return 0;
        }
        return prev + (100 / (SLIDE_DURATION / PROGRESS_INTERVAL));
      });
    }, PROGRESS_INTERVAL);
  };

  // Initialize first slide
  useEffect(() => {
    // Simulate image loading delay
    setTimeout(() => {
      setImageLoaded(true);
      // Then slide in the left panel
      setTimeout(() => {
        setLeftPanelVisible(true);
        startProgressTimer();
      }, 300);
    }, 500);
  }, []);

  const nextSlide = () => {
    if (!isTransitioning) {
      setIsTransitioning(true);
      setLeftPanelVisible(false);

      setTimeout(() => {
        setCurrentSlide((prev) => (prev === slides.length - 1 ? 0 : prev + 1));
        setImageLoaded(false);

        // Simulate new image loading
        setTimeout(() => {
          setImageLoaded(true);
          setTimeout(() => {
            setLeftPanelVisible(true);
            setIsTransitioning(false);
            startProgressTimer();
          }, 300);
        }, 200);
      }, 300);
    }
  };

  const prevSlide = () => {
    if (!isTransitioning) {
      setIsTransitioning(true);
      setLeftPanelVisible(false);

      setTimeout(() => {
        setCurrentSlide((prev) => (prev === 0 ? slides.length - 1 : prev - 1));
        setImageLoaded(false);

        setTimeout(() => {
          setImageLoaded(true);
          setTimeout(() => {
            setLeftPanelVisible(true);
            setIsTransitioning(false);
            startProgressTimer();
          }, 300);
        }, 200);
      }, 300);
    }
  };

  // Clean up intervals
  useEffect(() => {
    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current);
      if (progressRef.current) clearInterval(progressRef.current);
    };
  }, []);

  // Format the slide number with leading zero
  const formatSlideNumber = (num: number) => {
    return num < 10 ? `0${num}` : num;
  };

  // Circular progress component
  const CircularProgress = ({ progress, size = 48 }: { progress: number; size?: number }) => {
    const radius = (size - 4) / 2;
    const circumference = radius * 2 * Math.PI;
    const strokeDasharray = circumference;
    const strokeDashoffset = circumference - (progress / 100) * circumference;

    return (
      <div className="relative" style={{ width: size, height: size }}>
        <svg
          className="absolute inset-0 -rotate-90"
          width={size}
          height={size}
        >
          {/* Background circle */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke="rgba(255, 255, 255, 0.2)"
            strokeWidth="2"
            fill="none"
          />
          {/* Progress circle */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke="white"
            strokeWidth="2"
            fill="none"
            strokeDasharray={strokeDasharray}
            strokeDashoffset={strokeDashoffset}
            className="transition-all duration-100 ease-linear"
          />
        </svg>
      </div>
    );
  };

  return (
    <div className="relative w-full h-[600px] md:h-[700px] overflow-hidden bg-gray-100">
      {/* Current slide */}
      <div className="absolute inset-0 w-full h-full flex">
        {/* Right Image Panel - Loads first */}
        <div className="hidden md:block w-1/2 relative ml-auto">
          <div className={`absolute inset-0 transition-opacity duration-500 ${
            imageLoaded ? 'opacity-100' : 'opacity-0'
          }`}>
            <Image
              src={slides[currentSlide].image}
              alt={slides[currentSlide].title}
              fill
              className="object-cover"
              priority
              onLoad={() => setImageLoaded(true)}
            />
          </div>
        </div>

        {/* Left Content Panel - Slides in from left */}
        <div
          className={`absolute left-0 top-0 w-full md:w-1/2 h-full flex flex-col justify-center p-8 md:p-16 transform transition-transform duration-700 ease-out ${
            leftPanelVisible ? 'translate-x-0' : '-translate-x-full'
          }`}
          style={{ backgroundColor: slides[currentSlide].backgroundColor }}
        >
          <div className="text-white">
            <p className="text-xs md:text-sm font-medium tracking-[0.2em] mb-6 opacity-80">
              {slides[currentSlide].category}
            </p>
            <h2 className="text-2xl md:text-4xl lg:text-5xl font-light mb-8 leading-tight">
              {slides[currentSlide].title}
            </h2>
            <Link
              href={slides[currentSlide].buttonLink}
              className="inline-block border border-white text-white px-8 py-3 text-xs font-medium tracking-[0.15em] hover:bg-white hover:text-black transition-colors duration-300"
            >
              {slides[currentSlide].buttonText}
            </Link>
          </div>
        </div>
      </div>

      {/* Navigation Controls */}
      <div className="absolute bottom-8 left-8 md:left-16 z-20 flex items-center space-x-6 text-white">
        <div className="flex items-center space-x-4">
          <span className="text-2xl font-light">
            {formatSlideNumber(currentSlide + 1)}
          </span>
          <div className="w-16 h-px bg-white/30"></div>
          <span className="text-2xl text-white/40 font-light">
            {formatSlideNumber(slides.length)}
          </span>
        </div>
      </div>

      {/* Arrow Controls with Circular Progress */}
      <div className="absolute bottom-8 right-8 md:right-16 z-20 flex items-center space-x-4">
        <button
          onClick={prevSlide}
          className="relative w-12 h-12 flex items-center justify-center text-white hover:text-gray-300 transition-colors duration-300"
          aria-label="Previous slide"
          disabled={isTransitioning}
        >
          <div className="absolute inset-0">
            <CircularProgress progress={0} size={48} />
          </div>
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 relative z-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 19l-7-7 7-7" />
          </svg>
        </button>

        <button
          onClick={nextSlide}
          className="relative w-12 h-12 flex items-center justify-center text-white hover:text-gray-300 transition-colors duration-300"
          aria-label="Next slide"
          disabled={isTransitioning}
        >
          <div className="absolute inset-0">
            <CircularProgress progress={progress} size={48} />
          </div>
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 relative z-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>
    </div>
  );
};

export default HeroSlider;

